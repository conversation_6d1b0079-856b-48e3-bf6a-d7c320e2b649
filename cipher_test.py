#!/usr/bin/env python3

# Extract first letters from poem
poem_lines = [
    'Begin beneath the maple shade',
    'Where cherry petals softly fade',
    'From winding trails, you set your pace',
    'The journey starts, a change of place',
    'Walk eastward now, a few short streets',
    'To underground blue where rail lines meet',
    'Then board the train that hums eastbound',
    'Through tiled halls where echoes sound',
    'At the next exchange, ascend to light',
    'Pass the winding tracks and let the silver wings take flight',
    'A riverside trach that veers to the west',
    'Toward glass cad gates whore runways rest',
    'Sight hours long, the sky expands,',
    'As the ocean folds to distant lands.',
    'Crossing wide, near 3700 miles,',
    'New air, old bells, familiar styles.',
    'Past the halls and sliding doors,',
    'You\'ll find the tracks on distant floors.',
    'A silver pulls west in speed,',
    'Through vineyard lines and forested reed.',
    'The towns go by in half timbered rous,',
    'Their steeples thin, their windows close.',
    'At central halls, leave the ride;',
    'Rep north where plaza stones are wide,',
    'From whispering leaves to vaulted stone,',
    'From children\'s laughs to organ\'s tone',
    'The journey extends beyond the cieu;',
    'Let others words now speak to you.'
]

# Extract first letters
first_letters = ''.join([line[0] for line in poem_lines])
print(f'First letters: {first_letters}')

# Try all Caesar shifts
for shift in range(1, 26):
    result = ''
    for char in first_letters:
        if char.isalpha():
            base = ord('A') if char.isupper() else ord('a')
            result += chr((ord(char) - base + shift) % 26 + base)
        else:
            result += char
    
    print(f'Caesar {shift:2d}: {result}')
    if 'LNC25' in result.upper():
        print(f'*** FOUND LNC25 with Caesar {shift}! ***')

print()

# Try second letters
second_letters = ''
for line in poem_lines:
    if len(line) > 1:
        second_letters += line[1]

print(f'Second letters: {second_letters}')

for shift in range(1, 26):
    result = ''
    for char in second_letters:
        if char.isalpha():
            base = ord('A') if char.isupper() else ord('a')
            result += chr((ord(char) - base + shift) % 26 + base)
        else:
            result += char
    
    if 'LNC25' in result.upper():
        print(f'*** FOUND LNC25 in second letters with Caesar {shift}: {result} ***')

print()

# Try every 3rd character from the entire text
full_text = ' '.join(poem_lines).replace(' ', '').replace(',', '').replace('.', '').replace(';', '').replace('\'', '')
every_3rd = ''.join([full_text[i] for i in range(0, len(full_text), 3)])
print(f'Every 3rd char: {every_3rd}')

for shift in range(1, 26):
    result = ''
    for char in every_3rd:
        if char.isalpha():
            base = ord('A') if char.isupper() else ord('a')
            result += chr((ord(char) - base + shift) % 26 + base)
        else:
            result += char
    
    if 'LNC25' in result.upper():
        print(f'*** FOUND LNC25 in every 3rd with Caesar {shift}: {result} ***')
